<template>
  <n-cascader
    v-bind="cascaderAttrs"
    v-model:value="model"
    :options="filterOptions"
    :loading="loading"
  />
</template>

<script setup lang="ts">
  import { computed, onMounted, useAttrs } from 'vue';
  import { useAddressOptions } from '@/components/AddressInput/useAddressOptions';

  defineOptions({
    inheritAttrs: false,
  });

  const model = defineModel<string[] | string | undefined>({
    required: true,
    default: () => [],
  });
  const { loading, options, getOptions } = useAddressOptions();
  const attrs = useAttrs();

  const cascaderAttrs = computed<Record<string, unknown>>(() => {
    const resolved: Record<string, unknown> = { ...attrs };

    if (!('checkable' in resolved)) {
      resolved.checkable = false;
    }

    if (!('expand-trigger' in resolved) && !('expandTrigger' in resolved)) {
      resolved['expand-trigger'] = 'click';
    }

    if (!('leaf-only' in resolved) && !('leafOnly' in resolved)) {
      resolved['leaf-only'] = true;
    }

    if (!('clearable' in resolved)) {
      resolved.clearable = true;
    }

    if (!('placeholder' in resolved)) {
      resolved.placeholder = '请选择地区';
    }

    return resolved;
  });

  // options 中去除区县的数据
  const filterOptions = computed(() => {
    const res = options.value.map(({ children, ...restTop }) => {
      return {
        ...restTop,
        children: Array.isArray(children)
          ? children.map(({ children: ignored, ...restChild }) => ({
              ...restChild,
              children: undefined,
            }))
          : undefined,
      };
    });
    return res;
  });

  onMounted(() => {
    getOptions();
  });
</script>
