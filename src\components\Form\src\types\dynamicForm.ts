import type { ComponentType } from './index';
import type { FormItemRule } from 'naive-ui';
import type { CSSProperties, Component } from 'vue';
import type { GridProps, GridItemProps } from 'naive-ui/lib/grid';
import type { ButtonProps } from 'naive-ui/lib/button';
export const FiledOptions = {
  INPUT: 'input',
  TEXTAREA: 'textarea',
  PASSWORD: 'password',
  NUMBER: 'number',
  SELECT: 'select',
  RADIO: 'radioGroup',
  CHECKBOX: 'checkbox',
  CHECKBOX_GROUP: 'checkboxGroup',
  SWITCH: 'switch',
  DATE: 'date',
  DATE_RANGE: 'dateRange',
  TIME: 'time',
  TIME_RANGE: 'timeRange',
  DATETIME: 'datetime',
  DATETIME_RANGE: 'datetimeRange',
  UPLOAD: 'upload',
  RATE: 'rate',
  SLIDER: 'slider',
  COLOR_PICKER: 'colorPicker',
  CASCADER: 'cascader',
  TREE_SELECT: 'treeSelect',
  TRANSFER: 'transfer',
  MENTION: 'mention',
  DYNAMIC_INPUT: 'dynamicInput',
  DYNAMIC_TAGS: 'dynamicTags',
  DIVIDER: 'divider',
  CUSTOM: 'custom',
  DATA_TABLE: 'dataTable',
};
export type FieldType = (typeof FiledOptions)[keyof typeof FiledOptions];

// 字段类型枚举
// export enum FieldType {

// }

// 校验触发时机
export enum ValidateTrigger {
  BLUR = 'blur',
  CHANGE = 'change',
  INPUT = 'input',
  FOCUS = 'focus',
}

// 选项接口
export interface FieldOption {
  label: string;
  value: any;
  disabled?: boolean;
  children?: FieldOption[];
  [key: string]: any;
}

// 异步校验函数
export type AsyncValidator = (
  rule: FormItemRule,
  value: any,
  callback: (error?: string | Error) => void,
  source?: any,
  options?: any
) => Promise<void> | void;

// 增强的校验规则
export interface EnhancedFormItemRule extends Omit<FormItemRule, 'validator'> {
  validator?: AsyncValidator;
  // 条件校验：只有满足条件时才进行校验
  condition?: (formData: Record<string, any>) => boolean;
  // 自定义错误消息模板
  messageTemplate?: string;
  // 校验依赖字段
  dependencies?: string[];
}

// 动态表单字段配置
export interface DynamicFormField {
  // 基础配置
  field: string; // 字段名
  label: string; // 字段标签
  type: FieldType; // 字段类型
  component?: ComponentType; // 自定义组件类型
  optionComponent?: ComponentType; // 自定义选项组件类型
  // 显示配置
  placeholder?: string; // 占位符
  tooltip?: string; // 提示信息
  helpText?: string; // 帮助文本
  labelWidth?: number | string; // 标签宽度
  labelPlacement?: 'left' | 'top'; // 标签位置
  labelAlign?: 'left' | 'right'; // 标签对齐方式
  // 值配置
  defaultValue?: any; // 默认值
  value?: any; // 初始值

  // 状态配置
  required?: boolean; // 是否必填
  disabled?: boolean; // 是否禁用
  readonly?: boolean; // 是否只读
  hidden?: boolean; // 是否隐藏

  // 选项配置（用于选择类组件）
  options?: FieldOption[]; // 静态选项
  optionsApi?: string | (() => Promise<FieldOption[]>); // 动态选项API
  multiple?: boolean; // 是否多选

  // 组件属性
  componentProps?: Record<string, any>; // 组件属性

  // 校验配置
  rules?: EnhancedFormItemRule[]; // 校验规则
  validateTrigger?: ValidateTrigger[]; // 校验触发时机

  // 布局配置
  span?: number; // 栅格占位
  offset?: number; // 栅格偏移
  giProps?: GridItemProps; // 栅格项属性

  // 样式配置
  style?: CSSProperties; // 内联样式
  class?: string | string[]; // CSS类名

  // 插槽配置
  prefix?: string; // 前缀插槽
  suffix?: string; // 后缀插槽

  // 自定义渲染
  render?: (formData: Record<string, any>, field: DynamicFormField) => any;

  // 其他配置
  extra?: Record<string, any>; // 扩展配置
  children?: DynamicFormField[]; // 子字段
}

// 动态表单配置
export interface DynamicFormConfig {
  // 表单字段
  fields: DynamicFormField[];

  // 表单属性
  model?: Record<string, any>; // 表单数据模型
  labelWidth?: number | string; // 标签宽度
  labelPlacement?: 'left' | 'top'; // 标签位置
  labelAlign?: 'left' | 'right'; // 标签对齐方式
  size?: 'small' | 'medium' | 'large'; // 表单尺寸
  // inline?: boolean; // 是否内联
  disabled?: boolean; // 是否禁用整个表单
  validateOnBlur?: boolean; // 是否失焦时校验
  // 布局配置
  layout?: 'horizontal' | 'vertical' | 'inline'; // 布局方式
  gridProps?: GridProps; // 栅格属性
  columns?: number; // 列数

  // 按钮配置
  showSubmitButton?: boolean; // 是否显示提交按钮
  showResetButton?: boolean; // 是否显示重置按钮
  showCancelButton?: boolean; // 是否显示取消按钮
  submitButtonText?: string; // 提交按钮文本
  resetButtonText?: string; // 重置按钮文本
  cancelButtonText?: string; // 取消按钮文本
  submitButtonProps?: ButtonProps; // 提交按钮属性
  resetButtonProps?: ButtonProps; // 重置按钮属性
  cancelButtonProps?: ButtonProps; // 取消按钮属性

  // 校验配置
  validateOnRuleChange?: boolean; // 规则改变时是否校验
  validateOnValueChange?: boolean; // 值改变时是否校验

  // 事件回调
  onSubmit?: (formData: Record<string, any>) => void | Promise<void>;
  onReset?: () => void;
  onCancel?: () => void;
  onFieldChange?: (field: string, value: any, formData: Record<string, any>) => void;
  onValidate?: (field: string, valid: boolean, message?: string) => void;

  // 其他配置
  loading?: boolean; // 是否加载中
  readonly?: boolean; // 是否只读模式
  debug?: boolean; // 是否开启调试模式
}

// 表单实例方法
export interface DynamicFormInstance {
  // 数据操作
  getFormData: () => Record<string, any>;
  setFormData: (data: Record<string, any>) => void;
  getFieldValue: (field: string) => any;
  setFieldValue: (field: string, value: any) => void;
  resetForm: () => void;
  clearForm: () => void;

  // 校验操作
  validate: (fields?: string[]) => Promise<boolean>;
  validateField: (field: string) => Promise<boolean>;
  clearValidate: (fields?: string[]) => void;

  // 其他操作
  submit: () => void;
  reset: () => void;
  cancel: () => void;
}

// 表单字段组件映射
export interface FieldComponentMap {
  [key: string]: Component;
}

// 导出类型
export type { ComponentType, FormItemRule, GridProps, GridItemProps, ButtonProps };
