<template>
  <div class="dynamic-form" :class="formClass">
    <n-form
      ref="formRef"
      :model="formData"
      :rules="computedRules"
      :label-width="FormConfig.labelWidth"
      :label-placement="FormConfig.labelPlacement"
      :label-align="FormConfig.labelAlign || 'right'"
      :size="FormConfig.size"
      :disabled="FormConfig.disabled || FormConfig.loading || FormConfig.readonly"
      v-bind="formProps"
    >
      <n-grid v-bind="gridProps">
        <n-grid-item
          v-for="field in visibleFields"
          :key="field.field"
          :span="getFieldSpan(field)"
          :offset="field.offset"
          v-bind="field.giProps"
        >
          <n-form-item
            :label="field.label"
            :path="field.field"
            :label-width="field.labelWidth"
            :label-placement="field.labelPlacement"
            :show-require-mark="field.required"
            :label-align="field.labelAlign || FormConfig.labelAlign || 'right'"
          >
            <!-- 标签提示 -->
            <template #label v-if="field.tooltip">
              <div class="form-label-with-tooltip">
                <span>{{ field.label }}</span>
                <n-tooltip :content="field.tooltip">
                  <n-icon size="16" class="ml-1 text-gray-400 cursor-help">
                    <QuestionCircleOutlined />
                  </n-icon>
                </n-tooltip>
              </div>
            </template>

            <!-- 前缀插槽 -->
            <template v-if="field.prefix">
              <slot
                :name="field.prefix"
                :field="field"
                :value="getFieldValue(field.field)"
                :form-data="formData"
              >
                <span>{{ field.prefix }}</span>
              </slot>
            </template>
            <template v-if="field.render">
              <RenderComponent
                v-bind="getFieldProps(field)"
                :render="field.render"
                :value="getFieldValue(field.field)"
                @update:value="handleFieldChange(field.field, $event)"
              />
            </template>
            <template v-else>
              <!-- 表单控件 -->
              <template v-if="!needRenderOptions(field)">
                <template v-if="field.type === 'upload'">
                  <UploadFile
                    :fileList="getFieldValue(field.field)"
                    v-bind="getFieldProps(field)"
                    @update:file-list="handleFieldChange(field.field, $event)"
                  />
                </template>
                <template v-else-if="field.type === 'dataTable'">
                  <BaseTableV2
                    v-bind="field.componentProps"
                    @update:checked-row-keys="handleFieldChange(field.field, $event)"
                  />
                </template>
                <template v-else>
                  <component
                    :is="getFieldComponent(field)"
                    :value="getFieldValue(field.field)"
                    v-bind="getFieldProps(field)"
                    :disabled="field.disabled || FormConfig.disabled || FormConfig.loading"
                    :readonly="field.readonly || FormConfig.readonly"
                    @update:value="handleFieldChange(field.field, $event)"
                    @blur="handleFieldBlur(field.field)"
                    @focus="handleFieldFocus(field.field)"
                    :options="field.options"
                    :multiple="field.multiple"
                  />
                </template>
              </template>
              <template v-else>
                <component
                  :is="getFieldComponent(field)"
                  :value="getFieldValue(field.field)"
                  v-bind="getFieldProps(field)"
                  :disabled="field.disabled || FormConfig.disabled || FormConfig.loading"
                  :readonly="field.readonly || FormConfig.readonly"
                  @update:value="handleFieldChange(field.field, $event)"
                >
                  <component
                    :is="getOptionComponent(field)"
                    v-for="option in getFieldOptions(field)"
                    :key="option.value"
                    :value="option.value"
                    :label="option.label"
                    :disabled="option.disabled"
                  />
                </component>
              </template>
            </template>
            <!-- 后缀插槽 -->
            <template v-if="field.suffix">
              <slot
                :name="field.suffix"
                :field="field"
                :value="getFieldValue(field.field)"
                :form-data="formData"
              >
                <span>{{ field.suffix }}</span>
              </slot>
            </template>

            <!-- 帮助文本 -->
            <!--<div v-if="field.helpText" class="form-help-text">
              {{ field.helpText }}
            </div>
            -->
          </n-form-item>
        </n-grid-item>
      </n-grid>
      <!-- 操作按钮 -->
      <n-space v-if="showActionButtons" :span="24" class="form-actions" :justify="'start'">
        <n-button
          v-if="FormConfig.showSubmitButton"
          type="primary"
          :loading="submitLoading"
          :disabled="FormConfig.disabled"
          v-bind="FormConfig.submitButtonProps"
          @click="handleSubmit"
        >
          {{ FormConfig.submitButtonText || '提交' }}
        </n-button>

        <n-button
          v-if="FormConfig.showResetButton"
          :disabled="FormConfig.disabled"
          v-bind="FormConfig.resetButtonProps"
          @click="handleReset"
        >
          {{ FormConfig.resetButtonText || '重置' }}
        </n-button>

        <n-button
          v-if="FormConfig.showCancelButton"
          :disabled="FormConfig.disabled"
          v-bind="FormConfig.cancelButtonProps"
          @click="handleCancel"
        >
          {{ FormConfig.cancelButtonText || '取消' }}
        </n-button>
      </n-space>
    </n-form>

    <!-- 调试信息 -->
    <div v-if="FormConfig.debug" class="form-debug">
      <n-card title="调试信息" size="small">
        <pre>{{ JSON.stringify({ formData, errors: validationErrors }, null, 2) }}</pre>
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted, defineModel } from 'vue';
  import {
    NForm,
    NFormItem,
    NGrid,
    NGridItem,
    NButton,
    NSpace,
    NCard,
    NTooltip,
    NIcon,
    type FormInst,
  } from 'naive-ui';
  import UploadFile from '@/components/UploadFile/index.vue';
  import { QuestionCircleOutlined } from '@vicons/antd';
  import BaseTableV2 from '@/components/BaseTableV2/index.vue';
  import type {
    DynamicFormConfig,
    DynamicFormField,
    DynamicFormInstance,
    FieldOption,
    FieldType,
  } from './types/dynamicForm';
  import {
    getComponentByFieldType,
    getDefaultPropsByFieldType,
    ComponentMapManager,
  } from './utils/componentMap';
  import { validators } from '@/components/Form';
  import {
    getValueByPath,
    setValueByPath,
    isNestedPath,
    initializeNestedPaths,
  } from './utils/objectPath';
  // Props
  interface Props {
    // config: DynamicFormConfig;
    modelValue?: Record<string, any>;
  }
  const FormConfig = defineModel<DynamicFormConfig>('config', {
    default: () => ({}),
  });
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({}),
  });

  // Emits
  const emit = defineEmits<{
    'update:modelValue': [value: Record<string, any>];
    submit: [data: Record<string, any>];
    reset: [];
    cancel: [];
    'field-change': [field: string, value: any, formData: Record<string, any>];
    validate: [field: string, valid: boolean, message?: string];
  }>();

  // Refs
  const formRef = ref<FormInst>();
  const submitLoading = ref(false);
  const validationErrors = ref<Record<string, string>>({});

  // Reactive data
  const formData = reactive<Record<string, any>>({});

  // Computed
  const formClass = computed(() => ({
    'dynamic-form--loading': FormConfig.value.loading,
    'dynamic-form--readonly': FormConfig.value.readonly,
    'dynamic-form--disabled': FormConfig.value.disabled,
  }));

  const formProps = computed(() => {
    const { gridProps, ...otherProps } = FormConfig.value;
    return otherProps;
  });
  const RenderComponent = {
    props: {
      render: {
        type: Function,
      },
      value: {
        type: [String, Number, Boolean, Object, Array],
      },
    },
    setup(props, context) {
      return () => props.render?.(props, { ...context, formData });
    },
  };
  const gridProps = computed(() => ({
    cols: FormConfig.value.columns || 24,
    xGap: 16,
    yGap: 16,
    ...FormConfig.value.gridProps,
  }));

  const visibleFields = computed(() => {
    return FormConfig.value.fields.filter((field) => !field.hidden);
  });

  const showActionButtons = computed(() => {
    return (
      FormConfig.value.showSubmitButton ||
      FormConfig.value.showResetButton ||
      FormConfig.value.showCancelButton
    );
  });

  const computedRules = computed(() => {
    const rules: Record<string, any[]> = {};

    for (const field of FormConfig.value.fields) {
      if (field.rules && field.rules.length > 0) {
        rules[field.field] = field.rules.map((rule) => {
          // 处理条件校验
          if (rule.condition) {
            return {
              ...rule,
              validator: (ruleObj: any, value: any, callback: any) => {
                if (!rule.condition!(formData)) {
                  callback();
                  return;
                }
                if (rule.validator) {
                  rule.validator(ruleObj, value, callback, formData);
                } else {
                  callback();
                }
              },
            };
          }
          return rule;
        });
      } else if (field.required) {
        rules[field.field] = [
          validators.required(field.label ? `${field.label}为必填项` : '此字段为必填项'),
        ];
      }
    }
    console.log('rules', rules);

    return rules;
  });

  // Methods
  const initializeFormData = () => {
    // 清空现有数据
    Object.keys(formData).forEach((key) => {
      delete formData[key];
    });

    // 收集所有字段路径，用于初始化嵌套对象结构
    const fieldPaths = FormConfig.value.fields.map((field) => field.field);

    // 初始化嵌套对象结构
    initializeNestedPaths(formData, fieldPaths);

    // 设置默认值
    for (const field of FormConfig.value.fields) {
      const defaultValue = field.defaultValue !== undefined ? field.defaultValue : field.value;

      if (defaultValue !== undefined) {
        if (isNestedPath(field.field)) {
          setValueByPath(formData, field.field, defaultValue);
        } else {
          formData[field.field] = defaultValue;
        }
      }
    }

    // 合并外部传入的值
    if (props.modelValue) {
      // 对于嵌套路径，需要特殊处理
      for (const field of FormConfig.value.fields) {
        if (isNestedPath(field.field)) {
          const value = getValueByPath(props.modelValue, field.field);
          if (value !== undefined) {
            setValueByPath(formData, field.field, value);
          }
        }
      }

      // 合并非嵌套字段
      Object.assign(formData, props.modelValue);
    }
  };

  const getFieldComponent = (field: DynamicFormField) => {
    if (field.component) {
      return ComponentMapManager.getCustomComponent(field.component) || field.component;
    }

    return getComponentByFieldType(field.type);
  };

  const getFieldProps = (field: DynamicFormField) => {
    const defaultProps = getDefaultPropsByFieldType(field.type);
    return {
      ...defaultProps,
      ...field.componentProps,
      placeholder: field.placeholder || defaultProps.placeholder,
    };
  };

  const getFieldSpan = (field: DynamicFormField) => {
    if (field.span !== undefined) return field.span;
    const totalCols = FormConfig.value.columns || 24;
    return Math.floor(totalCols / (FormConfig.value.fields.length || 1));
  };

  const getFieldOptions = (field: DynamicFormField): FieldOption[] => {
    console.log('getFieldOptions', field.options);
    return field.options || [];
  };

  const needRenderOptions = (field: DynamicFormField) => {
    const optionTypes: FieldType[] = ['radio', 'checkboxGroup'] as FieldType[];
    return optionTypes.includes(field.type);
  };

  const getOptionComponent = (field: DynamicFormField) => {
    if (field.optionComponent) {
      return ComponentMapManager.getCustomComponent(field.optionComponent) || field.optionComponent;
    }
    switch (field.type) {
      case 'radio':
        return 'NRadio';
      case 'checkboxGroup':
        return 'NCheckbox';
      default:
        return 'div';
    }
  };

  // 获取字段值，支持嵌套路径
  const getFieldValue = (fieldName: string) => {
    if (isNestedPath(fieldName)) {
      return getValueByPath(formData, fieldName);
    }
    return formData[fieldName];
  };

  const handleFieldChange = async (fieldName: string, value: any) => {
    // 支持嵌套路径的字段值设置
    if (isNestedPath(fieldName)) {
      setValueByPath(formData, fieldName, value);
    } else {
      formData[fieldName] = value;
    }

    console.log('formData变化', formData);

    // 触发事件
    emit('field-change', fieldName, value, { ...formData });
    emit('update:modelValue', { ...formData });
  };

  const handleFieldBlur = (fieldName: string) => {
    // 字段失焦时校验
    if (FormConfig.value.validateOnBlur) {
      validateField(fieldName);
    }
  };

  const handleFieldFocus = (fieldName: string) => {
    // 字段聚焦时清除错误
    if (validationErrors.value[fieldName]) {
      delete validationErrors.value[fieldName];
    }
  };

  const validateField = async (fieldName: string): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      if (validationErrors.value[fieldName]) {
        delete validationErrors.value[fieldName];
      }
      emit('validate', fieldName, true);
      return true;
    } catch (errors: any) {
      if (errors && errors.length > 0) {
        const fieldError = errors.find((error: any) => error.field === fieldName);
        if (fieldError) {
          validationErrors.value[fieldName] = fieldError.message || '';
          emit('validate', fieldName, false, fieldError.message);
        }
      }
      return false;
    }
  };

  const validate = async (fields?: string[]): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      return false;
    }
  };

  const handleSubmit = async () => {
    if (submitLoading.value) return;

    submitLoading.value = true;

    try {
      const isValid = await validate();
      if (isValid) {
        emit('submit', { ...formData });
        if (FormConfig.value.onSubmit) {
          await FormConfig.value.onSubmit({ ...formData });
        }
      }
    } catch (error) {
      console.error('表单提交失败:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  const handleReset = () => {
    formRef.value?.restoreValidation();
    console.log('重置');
    initializeFormData();
    console.log(formData, '---重置表单');

    emit('reset');
    emit('update:modelValue', { ...formData });
    if (FormConfig.value.onReset) {
      FormConfig.value.onReset();
    }
  };

  const handleCancel = () => {
    emit('cancel');
    if (FormConfig.value.onCancel) {
      FormConfig.value.onCancel();
    }
  };

  // 暴露的实例方法
  const getInstance = (): DynamicFormInstance => ({
    getFormData: () => ({ ...formData }),
    setFormData: (data: Record<string, any>) => {
      // 清空现有数据
      Object.keys(formData).forEach((key) => {
        delete formData[key];
      });

      // 初始化嵌套对象结构
      const fieldPaths = FormConfig.value.fields.map((field) => field.field);
      initializeNestedPaths(formData, fieldPaths);

      // 设置新数据，支持嵌套路径
      for (const field of FormConfig.value.fields) {
        if (isNestedPath(field.field)) {
          const value = getValueByPath(data, field.field);
          if (value !== undefined) {
            setValueByPath(formData, field.field, value);
          }
        }
      }

      // 合并非嵌套字段
      Object.assign(formData, data);
      emit('update:modelValue', { ...formData });
    },
    getFieldValue: (field: string) => getFieldValue(field),
    setFieldValue: (field: string, value: any) => {
      if (isNestedPath(field)) {
        setValueByPath(formData, field, value);
      } else {
        formData[field] = value;
      }
      handleFieldChange(field, value);
    },
    resetForm: handleReset,
    clearForm: () => {
      Object.keys(formData).forEach((key) => {
        delete formData[key];
      });
      emit('update:modelValue', {});
    },
    validate,
    validateField,
    clearValidate: (fields?: string[]) => {
      if (fields) {
        fields.forEach((field) => {
          if (validationErrors.value[field]) {
            delete validationErrors.value[field];
          }
        });
      } else {
        Object.keys(validationErrors.value).forEach((key) => {
          delete validationErrors.value[key];
        });
      }
      formRef.value?.restoreValidation();
    },
    submit: handleSubmit,
    reset: handleReset,
    cancel: handleCancel,
  });

  // 监听外部数据变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue) {
        // 对于嵌套路径，需要特殊处理
        for (const field of FormConfig.value.fields) {
          if (isNestedPath(field.field)) {
            const value = getValueByPath(newValue, field.field);
            if (value !== undefined) {
              setValueByPath(formData, field.field, value);
            }
          }
        }

        // 合并非嵌套字段
        Object.assign(formData, newValue);
      }
    },
    { deep: true }
  );

  // 生命周期
  onMounted(() => {
    initializeFormData();
  });

  // 暴露实例
  defineExpose(getInstance());
</script>

<style lang="less" scoped>
  .dynamic-form {
    &--loading {
      pointer-events: none;
      opacity: 0.6;
    }
    &--readonly {
      .n-form-item {
        pointer-events: not-allowed;
      }
    }
    .form-label-with-tooltip {
      display: flex;
      align-items: center;
    }

    .form-help-text {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
      line-height: 1.4;
    }

    .form-actions {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }

    .form-debug {
      margin-top: 16px;

      pre {
        font-size: 12px;
        max-height: 300px;
        overflow: auto;
      }
    }
  }
</style>
