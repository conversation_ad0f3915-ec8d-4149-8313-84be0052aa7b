import { BasicColumn } from '@/components/Table';
import dayjs from 'dayjs';
import {
  FollowStatusMap,
  ClueStatusMap,
  ClueFollowerMap,
  ContactStatusMap,
  IsAddWeChatMap,
  IntentionDegreeMap,
  ClueStoreTypeMap,
  CommunicationMap,
  AssignTypeMap,
  ConvertTypeMap,
  ConvertStatusMap,
  ClueTypeMap,
  InvalidTypeMap,
  ClueBidTypeMap,
} from '../enum';
import { createTitleWithTooltip } from '@/utils/tableUtils';
import { useRoleOptions } from '@/components/RoleSelect/useRoleOptions';
import { useAdvflowMediaOptions } from '@/components/AdvflowMediaSelect/useAdvflowMediaOptions';

export interface ListData {
  id: number;
  name: string;
  mobileNo: string;
  ownerUserName: string;
  followStatus: number;
  clueStatus: number;
  clueSource: number;
  channelCode: number;
  mediaPlatformSource: number;
  clueFollower: number;
  communicationStatus: number;
  addWeChat: number;
  intent: number;
  clueStoreType: number;
  createTime: number;
  triageTime: number;
  lastFollowTime: number;
  communicationDetail: number;
  assignType: number;
  convertType: number;
  convertStatus: number;
  clueType: number;
  clueBidType: number;
  invalidType: number;
  invalidRemark: string;
  roleId?: number;
  apiCode?: string;
  clueEnableAssign: number;
}

export const columns: BasicColumn<ListData>[] = [
  { type: 'selection' as any, key: 'selection' },
  {
    title: '线索ID',
    key: 'id',
    width: 80,
    align: 'center',
  },
  {
    title: '姓名',
    key: 'name',
    width: 80,
    align: 'center',
  },
  {
    title: '电话',
    key: 'mobileNo',
    width: 120,
    align: 'center',
  },
  {
    title: '手机号MD5',
    key: 'mobileMd5',
    width: 300,
    align: 'center',
  },
  {
    title: '线索出价类型',
    key: 'clueBidType',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ClueBidTypeMap[record.clueBidType]}</span>;
    },
  },
  {
    title: createTitleWithTooltip({
      title: '分配类型',
      tooltip: '仅可分配客户可被认领/分配',
    }),
    key: 'assignType',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      return <span>{AssignTypeMap[record.assignType] || '-'}</span>;
    },
  },
  {
    title: createTitleWithTooltip({
      title: '转化类型',
      tooltip: '客户的转化类型，包括放款和分发',
    }),
    key: 'convertType',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ConvertTypeMap[record.convertType] || '-'}</span>;
    },
  },
  {
    title: createTitleWithTooltip({
      title: '转化状态',
      tooltip: '转化类型对应的转化状态，包括已转化和未转化',
    }),
    key: 'convertStatus',
    align: 'center',
    width: 120,
    render: (record: ListData) => {
      return <span>{ConvertStatusMap[record.convertStatus] || '-'}</span>;
    },
  },
  {
    title: '归属角色',
    key: 'roleId',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      const { roleOptions } = useRoleOptions();
      if (!record.roleId) return '-';

      const roleLabel = roleOptions.value.find((item) => item.id === record.roleId)?.name;
      return roleLabel || '-';
    },
  },
  {
    title: '归属人',
    key: 'blongUserName',
    width: 90,
    align: 'center',
  },
  {
    title: '跟进状态',
    key: 'followStatus',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{FollowStatusMap[record.followStatus]}</span>;
    },
  },
  {
    title: '线索状态',
    key: 'clueStatus',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ClueStatusMap[record.clueStatus]}</span>;
    },
  },
  {
    title: '线索来源',
    key: 'channelName',
    width: 90,
    align: 'center',
  },
  {
    title: '来源媒体',
    key: 'mediaPlatformSource',
    width: 90,
    align: 'center',
  },
  {
    title: '投放平台',
    key: 'apiCode',
    width: 120,
    align: 'center',
    render: (record: ListData) => {
      const { advflowMediaOptions } = useAdvflowMediaOptions();
      if (!record.apiCode) return '-';

      const platformLabel = advflowMediaOptions.value.find(
        (item) => item.platformCode === record.apiCode
      )?.platformName;
      return platformLabel || '-';
    },
  },
  {
    title: '来源渠道',
    key: 'sourceChannelCode',
    width: 90,
    align: 'center',
  },
  {
    title: '线索类型',
    key: 'clueType',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ClueTypeMap[record.clueType]}</span>;
    },
  },
  {
    title: '无效类型',
    key: 'invalidType',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{InvalidTypeMap[record.invalidType]}</span>;
    },
  },
  {
    title: '无效说明',
    key: 'invalidRemark',
    width: 90,
    align: 'center',
  },
  {
    title: '线索跟进方',
    key: 'clueFollower',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ClueFollowerMap[record.clueFollower]}</span>;
    },
  },
  {
    title: '通讯状态',
    key: 'communicationStatus',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ContactStatusMap[record.communicationStatus]}</span>;
    },
  },
  {
    title: '通讯详情',
    key: 'communicationDetail',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{CommunicationMap[record.communicationDetail]}</span>;
    },
  },
  {
    title: '是否加微',
    key: 'addWeChat',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{IsAddWeChatMap[record.addWeChat]}</span>;
    },
  },
  {
    title: '意向度',
    key: 'intent',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{IntentionDegreeMap[record.intent]}</span>;
    },
  },
  {
    title: '入库方式',
    key: 'clueStoreType',
    width: 90,
    align: 'center',
    render: (record: ListData) => {
      return <span>{ClueStoreTypeMap[record.clueStoreType]}</span>;
    },
  },
  {
    title: '线索创建时间',
    key: 'createTime',
    width: 180,
    align: 'center',
    sorter: (a, b) => a.createTime - b.createTime,
    render: (record: ListData) => {
      return (
        <span>
          {record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '线索提交时间',
    key: 'clueEnableAssign',
    width: 180,
    align: 'center',
    sorter: (a, b) => a.clueEnableAssign - b.clueEnableAssign,
    render: (record: ListData) => {
      return (
        <span>
          {record.clueEnableAssign
            ? dayjs(record.clueEnableAssign).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </span>
      );
    },
  },
  {
    title: createTitleWithTooltip({
      title: '线索分发时间',
      tooltip: '线索落入我的客户列表时间',
    }),
    key: 'triageTime',
    width: 180,
    align: 'center',
    sorter: (a, b) => a.triageTime - b.triageTime,
    render: (record: ListData) => {
      return (
        <span>
          {record.triageTime ? dayjs(record.triageTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '最近跟进时间',
    key: 'lastFollowTime',
    width: 180,
    align: 'center',
    sorter: (a, b) => a.lastFollowTime - b.lastFollowTime,
    render: (record: ListData) => {
      return (
        <span>
          {record.lastFollowTime ? dayjs(record.lastFollowTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
];
