<template>
  <div class="nested-form-demo">
    <n-card title="嵌套对象表单数据示例" class="mb-4">
      <DynamicForm
        ref="formRef"
        v-model:config="formConfig"
        v-model="formData"
        @submit="handleSubmit"
        @field-change="handleFieldChange"
      />
    </n-card>

    <n-card title="表单数据预览" size="small">
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { NCard } from 'naive-ui';
  import { DynamicForm, FiledOptions, validators } from '@/components/Form';
  import type { DynamicFormInstance, DynamicFormConfig } from '@/components/Form/src/types/dynamicForm';

  // 表单引用
  const formRef = ref<DynamicFormInstance>();

  // 表单数据
  const formData = ref({
    // 基础字段
    customerName: '',
    customerPhone: '',
    
    // 嵌套对象字段
    vehicleInfo: {
      bodyColor: '',
      interiorColor: '',
      licenseCity: '',
      factoryDate: null,
      transferCount: '',
      vehicleStatus: null,
      licensePlateNumber: '',
    },
    
    personalInfo: {
      name: '',
      email: '',
      educationLevel: null,
      maritalStatus: null,
      childCount: null,
      residenceAddress: '',
      company: '',
      monthlyIncome: null,
    }
  });

  // 表单配置
  const formConfig = reactive<DynamicFormConfig>({
    labelWidth: 120,
    labelPlacement: 'left',
    size: 'medium',
    columns: 24,
    showSubmitButton: true,
    showResetButton: true,
    debug: true,
    fields: [
      // 基础信息分组
      {
        field: 'basicInfo',
        label: '基础信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'customerName',
        label: '客户姓名',
        type: FiledOptions.INPUT,
        required: true,
        span: 8,
        rules: [validators.required('请输入客户姓名')],
      },
      {
        field: 'customerPhone',
        label: '客户电话',
        type: FiledOptions.INPUT,
        required: true,
        span: 8,
        rules: [
          validators.required('请输入客户电话'),
          validators.phone('请输入正确的手机号码'),
        ],
      },
      
      // 车辆信息分组
      {
        field: 'vehicleInfo',
        label: '车辆信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'vehicleInfo.bodyColor',
        label: '车身颜色',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入车身颜色',
      },
      {
        field: 'vehicleInfo.interiorColor',
        label: '内饰颜色',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入内饰颜色',
      },
      {
        field: 'vehicleInfo.licenseCity',
        label: '上牌城市',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入上牌城市',
      },
      {
        field: 'vehicleInfo.factoryDate',
        label: '出厂日期',
        type: FiledOptions.DATE,
        span: 8,
      },
      {
        field: 'vehicleInfo.transferCount',
        label: '过户次数',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入过户次数',
      },
      {
        field: 'vehicleInfo.vehicleStatus',
        label: '车辆状态',
        type: FiledOptions.SELECT,
        span: 8,
        options: [
          { label: '正常', value: 1 },
          { label: '事故车', value: 2 },
          { label: '泡水车', value: 3 },
        ],
      },
      {
        field: 'vehicleInfo.licensePlateNumber',
        label: '车牌号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入车牌号',
      },
      
      // 个人信息分组
      {
        field: 'personalInfo',
        label: '个人信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'personalInfo.name',
        label: '姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入姓名',
      },
      {
        field: 'personalInfo.email',
        label: '邮箱',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入邮箱',
        rules: [validators.email('请输入正确的邮箱格式')],
      },
    ],
  });

  // 处理表单提交
  const handleSubmit = (data: Record<string, any>) => {
    console.log('表单提交数据:', data);
    window.$message?.success('表单提交成功！');
  };

  // 处理字段变化
  const handleFieldChange = (field: string, value: any, formData: Record<string, any>) => {
    console.log('字段变化:', { field, value, formData });
  };
</script>

<style lang="less" scoped>
  .nested-form-demo {
    padding: 20px;
    
    pre {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 400px;
      overflow: auto;
    }
  }
</style>
