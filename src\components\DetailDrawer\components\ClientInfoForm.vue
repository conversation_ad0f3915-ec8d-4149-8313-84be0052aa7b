<script setup lang="tsx">
  import { DynamicForm, FiledOptions, validators } from '@/components/Form';
  import type {
    DynamicFormInstance,
    DynamicFormConfig,
  } from '@/components/Form/src/types/dynamicForm';
  import { ref, computed } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import CitySelect from '@/components/CitySelect/index.vue';

  defineProps<{
    showForm: 'yishun' | 'deyi';
  }>();
  interface IUpdateCustomerModalFormModel {
    clueId: number | null;
    vehicleInfo: {
      id: number | null;
      bodyColor: string;
      interiorColor: string;
      licenseCity: string;
      factoryDate: string | null;
      transferCount: string;
      vehicleStatus: number | null;
      carOfPersonStatus: number | null;
      clueId: number | null;
      carThreeHundred: string | null;
      licensePlateNumber: string;
    };
    personalInfo: {
      id: number | null;
      threeElementsStatus: number | null;
      email: string;
      educationLevel: number | null;
      maritalStatus: number | null;
      childCount: number | null;
      residenceAddress: string;
      houseType: number | null;
      company: string;
      companyAddress: string;
      companyPhone: string | null;
      companyNature: number | null;
      monthlyIncome: number | null;
      contactName: string;
      contactPhone: string | null;
      contactRelation: number | null;
      clueId: number | null;
      idCardNumber: string | null;
      name: string | null;
      cityCode: number | string | null;
      cityName: string | null;
      provinceName: string | null;
      provinceCode: number | string | null;
    };
    certificateInfo: {
      id: number | null;
      idCardImgUrl: string;
      idCardImgBackUrl: string;
      drivingLicenseImgUrl: string;
      drivingLicenseImgBackUrl: string;
      paperDriverLicenseImgUrl: string;
      paperDriverLicenseImgBackUrl: string;
      bankCardImgUrl: string;
      bankCardBackUrl: string;
      clueId: number | null;
    };
    otherDocuments: {
      id: number | null;
      vehiclePhotos: string[];
      contactListMedias: string[];
      traffic12123Medias: string[];
      bankStatements: string[];
      bankAccountInfos: string[];
      insurances: string[];
      clueId: number | null;
    };
  }
  // 表单引用
  const linkageFormRef = ref<DynamicFormInstance>();
  const citySelectRef = ref();
  const DEFAULT_FORM_MODEL: IUpdateCustomerModalFormModel = {
    clueId: null,
    vehicleInfo: {
      id: null,
      bodyColor: '',
      interiorColor: '',
      licenseCity: '',
      factoryDate: null,
      transferCount: '',
      vehicleStatus: null,
      carOfPersonStatus: null,
      clueId: null,
      carThreeHundred: null,
      licensePlateNumber: '',
    },
    personalInfo: {
      id: null,
      name: '',
      cityCode: null,
      cityName: '',
      provinceName: '',
      provinceCode: null,
      threeElementsStatus: null,
      email: '',
      educationLevel: null,
      maritalStatus: null,
      childCount: null,
      residenceAddress: '',
      houseType: null,
      company: '',
      companyAddress: '',
      companyPhone: null,
      companyNature: null,
      monthlyIncome: null,
      contactName: '',
      contactPhone: null,
      contactRelation: null,
      clueId: null,
      idCardNumber: null,
    },
    certificateInfo: {
      id: null,
      idCardImgUrl: '',
      idCardImgBackUrl: '',
      drivingLicenseImgUrl: '',
      drivingLicenseImgBackUrl: '',
      paperDriverLicenseImgUrl: '',
      paperDriverLicenseImgBackUrl: '',
      bankCardImgUrl: '',
      bankCardBackUrl: '',
      clueId: null,
    },
    otherDocuments: {
      id: null,
      vehiclePhotos: [],
      contactListMedias: [],
      traffic12123Medias: [],
      bankStatements: [],
      bankAccountInfos: [],
      insurances: [],
      clueId: null,
    },
  };

  // 表单数据
  const linkageFormData = ref<any>(cloneDeep(DEFAULT_FORM_MODEL));
  // 联动表单配置
  const linkageFormConfig = computed(() => ({
    labelWidth: 90,
    columns: 24,
    showActionButtons: false,
    labelPlacement: 'left',
    onSubmit: async (data) => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      //成功之后的处理
      linkageFormRef.value?.resetForm();
      return data;
    },
    fields: [
      {
        field: 'vehicleInfo',
        label: '车辆信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'vehicleInfo.bodyColor',
        label: '车身颜色',
        type: FiledOptions.INPUT,
        span: 8,

        // componentProps: {
        //   maxlength: 10,
        // },
        // render: (props, ctx) => {
        //   return (
        //     <n-select
        //       value={props.value}
        //       options={[
        //         { label: '个人用户', value: 'personal' },
        //         { label: '企业用户', value: 'enterprise' },
        //       ]}
        //       onUpdateValue={(value) => {
        //         ctx.emit('update:value', value);
        //       }}
        //     />
        //   );
        // },
        // options: [
        //   { label: '个人用户', value: 'personal' },
        //   { label: '企业用户', value: 'enterprise' },
        // ],
      },
      //   {
      //     field: 'vehicleInfo.interiorColor',
      //     label: '内饰颜色',
      //     type: FiledOptions.INPUT,
      //     span: 8,
      //   },
      //   {
      //     field: 'vehicleInfo.licenseCity',
      //     label: '上牌城市',
      //     type: FiledOptions.INPUT,
      //     span: 8,
      //     render: (props, ctx) => {
      //       return (
      //         <CitySelect
      //           change-on-select
      //           multiple={false}
      //           ref={citySelectRef}
      //           onUpdate:value={(value) => {
      //             ctx.emit('update:value', value);
      //           }}
      //           value={props.value}
      //         />
      //       );
      //     },
      //   },
      //   {
      //     field: 'vehicleInfo.factoryDate',
      //     label: '出厂日期',
      //     type: FiledOptions.DATE,
      //     span: 8,
      //   },
      //   {
      //     field: 'personalInfo.cityCode',
      //     label: '所在城市',
      //     type: FiledOptions.INPUT,
      //     span: 8,
      //     render: (props, ctx) => {
      //       return (
      //         <CitySelect
      //           change-on-select
      //           multiple={false}
      //           onUpdate:value={(value) => {
      //             ctx.emit('update:value', value);
      //           }}
      //           value={props.value}
      //         />
      //       );
      //     },
      //   },
      {
        field: 'username',
        label: '用户名',
        type: FiledOptions.INPUT,
        span: 12,
        rules: [
          validators.required('请输入用户名'),
          validators.remote(async (value) => {
            // 模拟异步校验
            await new Promise((resolve) => setTimeout(resolve, 500));
            return value !== 'admin'; // admin 用户名不可用
          }, '用户名已存在'),
        ],
      },
      {
        field: 'personalName',
        label: '个人姓名',
        type: FiledOptions.INPUT,
        span: 12,
        hidden: linkageFormData.value?.userType !== 'personal',
      },
      {
        field: 'companyName',
        label: '公司名称',
        type: FiledOptions.INPUT,
        span: 12,
        hidden: linkageFormData.value?.userType !== 'enterprise',
      },
      {
        field: 'hasContract',
        label: '是否签署合同',
        type: FiledOptions.SWITCH,
        span: 24,
      },

      {
        field: 'contractFile',
        label: '合同文件',
        type: FiledOptions.UPLOAD,
        span: 24,
        required: true,
        hidden: linkageFormData.value.hasContract !== true,
        componentProps: {
          accept: '.pdf,.doc,.docx',
          maxSize: 15,
          maxCount: 2,
          multiple: true,
        },
        suffix: '文件大小不能超过1M',
        rules: [validators.arrayLength(1, 2, '请上传文件'), validators.fileSize(1, 'MB')],
      },
    ],
  }));
</script>
<template>
  <div>
    <DynamicForm
      ref="linkageFormRef"
      v-model:config="linkageFormConfig as DynamicFormConfig"
      v-model="linkageFormData"
    />
  </div>
</template>
